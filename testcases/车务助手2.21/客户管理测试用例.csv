用例编号,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,【现场照配置】页面显示验证,P1,,"1. 进入现场照配置页面，查看页面标题显示
2. 查看搜索框字段显示
3. 查看列表字段显示
4. 查看数据显示内容和排序","1. 页面标题显示「首页/现场照配置」
2. 搜索框显示客户名称、子公司名称、抵押权人字段
3. 列表显示客户名称、子公司名称、抵押权人、配置时间、操作字段
4. 显示所有客户订单的车主现场照配置为选填项的规则，按新增时间倒序排列"
TC002,【现场照配置】客户名称搜索验证,P1,存在现场照配置数据,"1. 在客户名称搜索框中输入存在的客户名称左半部分，点击查询，观察搜索结果
2. 在客户名称搜索框中输入存在的客户名称右半部分，点击查询，观察搜索结果
3. 在客户名称搜索框中输入完整的客户名称，点击查询，观察搜索结果","1. 列表显示包含该左半部分客户名称的所有符合条件数据
2. 列表显示为空
3. 列表显示该完整客户名称的所有符合条件数据"
TC003,【现场照配置】子公司名称搜索验证,P1,存在现场照配置数据,"1. 在子公司名称搜索框中输入存在的子公司名称左半部分，点击查询，观察搜索结果
2. 在子公司名称搜索框中输入存在的子公司名称右半部分，点击查询，观察搜索结果
3. 在子公司名称搜索框中输入完整的子公司名称，点击查询，观察搜索结果","1. 列表显示包含该左半部分子公司名称的所有符合条件数据
2. 列表显示为空
3. 列表显示该完整子公司名称的所有符合条件数据"
TC004,【现场照配置】抵押权人搜索验证,P1,存在现场照配置数据,"1. 在抵押权人搜索框中输入存在的抵押权人左半部分，点击查询，观察搜索结果
2. 在抵押权人搜索框中输入存在的抵押权人右半部分，点击查询，观察搜索结果
3. 在抵押权人搜索框中输入完整的抵押权人，点击查询，观察搜索结果","1. 列表显示包含该左半部分抵押权人的所有符合条件数据
2. 列表显示为空
3. 列表显示该完整抵押权人的所有符合条件数据"
TC005,【现场照配置】组合查询验证,P1,存在现场照配置数据,"1. 在搜索框中输入客户名称、子公司名称、抵押权人，点击[查询]按钮，观察搜索结果
2. 点击[重置]按钮，观察页面数据变化","1. 列表显示同时满足所有搜索条件的符合条件数据
2. 所有搜索条件清空，列表显示全部数据"
TC006,【现场照配置】配置按钮弹窗显示验证,P1,,"1. 点击[配置]按钮，观察弹窗显示
2. 查看弹窗标题和文字说明","1. 显示【配置车主现场照下单规则】弹窗
2. 弹窗文字说明显示「默认下单时，车主现场照是必填项」和「配置后，车主现场照变为选填项，仅应用在接口下单！」"
TC007,【现场照配置】配置字段验证,P0,,"1. 在配置弹窗中不填写客户名称，点击[确定]按钮，观察提示信息
2. 填写客户名称，其他不填写，点击[确定]按钮，观察配置结果
3. 填写客户名称和子公司名称，其他不填写，点击[确定]按钮，观察配置结果
4. 填写客户名称、子公司名称和抵押权人，点击[确定]按钮，观察配置结果","1. 给出客户名称必填的错误提示
2. 生成一条现场照配置规则
3. 生成一条现场照配置规则
4. 生成一条现场照配置规则"
TC008,【现场照配置】子公司名称联动验证,P1,,"1. 在配置弹窗中不选择客户名称，查看子公司名称字段状态
2. 选择客户名称，查看子公司名称下拉选项","1. 子公司名称为空，不可选择
2. 可以选择已选客户下的所有子公司"
TC009,【现场照配置】抵押权人来源验证,P1,,"1. 在配置弹窗中查看抵押权人下拉选项来源","1. 抵押权人选项来自政策管家"
TC010,【现场照配置】重复配置验证,P0,,"1. 创建一条客户名称的现场照配置规则
2. 尝试再次新增该客户的现场照配置规则
3. 创建一条客户名称+子公司的现场照配置规则
4. 尝试再次新增该客户+子公司的现场照配置规则
5. 创建一条客户名称+子公司+抵押权人的现场照配置规则
6. 尝试再次新增该客户+子公司+抵押权人的现场照配置规则","1. 第一条客户规则创建成功
2. 提示不能重复新增该客户的现场照配置规则
3. 客户+子公司规则创建成功
4. 提示不能重复新增该客户+子公司的现场照配置规则
5. 客户+子公司+抵押权人规则创建成功
6. 提示不能重复新增该客户+子公司+抵押权人的现场照配置规则"
TC011,【现场照配置】应用优先级验证,P0,"设置了客户+子公司+抵押权人、客户+子公司、客户的现场照配置","1. 创建符合客户+子公司+抵押权人条件的订单，观察现场照设置
2. 创建符合客户+子公司条件的订单，观察现场照设置
3. 创建符合客户条件的订单，观察现场照设置
4. 创建不符合任何配置条件的订单，观察现场照设置","1. 取客户+子公司+抵押权人的设置，现场照选填
2. 取客户+子公司的设置，现场照选填
3. 取客户的设置，现场照选填
4. 现场照必填"
TC012,【现场照配置】下单入口验证,P1,存在现场照配置规则,"1. 通过普通线下抵押接口下单，观察现场照配置应用情况
2. 通过普通线下解押接口下单，观察现场照配置应用情况","1. 普通线下抵押接口下单时正确应用现场照配置规则
2. 普通线下解押接口下单时正确应用现场照配置规则"
TC013,【现场照配置】删除按钮功能验证,P1,存在现场照配置数据,"1. 点击任意一条记录的[删除]按钮，观察弹窗显示
2. 在删除确认弹窗中点击[取消]按钮，观察弹窗和数据变化
3. 再次点击[删除]按钮，在删除确认弹窗中点击[确定]按钮，观察弹窗和数据变化","1. 显示删除确认弹窗，提示文字为「确定要删除本条客户的车主现场照下单规则吗？」
2. 弹窗关闭，该条数据未被删除，仍显示在列表中
3. 弹窗关闭，该条数据被删除，不再显示在列表中"
TC014,【抵押权人签署配置】页面显示验证,P1,,"1. 进入抵押权人签署配置页面，查看页面标题显示
2. 查看搜索框字段显示
3. 查看列表字段显示
4. 查看数据显示内容和排序","1. 页面标题显示「首页/抵押权人签署配置」
2. 搜索框显示客户名称、抵押权人字段
3. 列表显示客户名称、子公司名称、抵押权人、签署规则、配置时间、操作字段
4. 显示所有客户订单的抵押权人签署配置为自己用印or不签署的规则，按新增时间倒序排列"
TC015,【抵押权人签署配置】客户名称搜索验证,P1,存在抵押权人签署配置数据,"1. 在客户名称搜索框中输入存在的客户名称左半部分，点击查询，观察搜索结果
2. 在客户名称搜索框中输入存在的客户名称右半部分，点击查询，观察搜索结果
3. 在客户名称搜索框中输入完整的客户名称，点击查询，观察搜索结果","1. 列表显示包含该左半部分客户名称的所有符合条件数据
2. 列表显示为空
3. 列表显示该完整客户名称的所有符合条件数据"
TC016,【抵押权人签署配置】抵押权人搜索验证,P1,存在抵押权人签署配置数据,"1. 在抵押权人搜索框中输入存在的抵押权人左半部分，点击查询，观察搜索结果
2. 在抵押权人搜索框中输入存在的抵押权人右半部分，点击查询，观察搜索结果
3. 在抵押权人搜索框中输入完整的抵押权人，点击查询，观察搜索结果","1. 列表显示包含该左半部分抵押权人的所有符合条件数据
2. 列表显示为空
3. 列表显示该完整抵押权人的所有符合条件数据"
TC017,【抵押权人签署配置】组合查询验证,P1,存在抵押权人签署配置数据,"1. 在搜索框中输入客户名称、抵押权人，点击[查询]按钮，观察搜索结果
2. 点击[重置]按钮，观察页面数据变化","1. 列表显示同时满足所有搜索条件的符合条件数据
2. 所有搜索条件清空，列表显示全部数据"
TC018,【抵押权人签署配置】签署规则应用验证,P0,,"1. 创建未配置签署规则的订单，观察抵押权人签署流程
2. 创建配置了「抵押权人自己用印」规则的订单，观察抵押权人签署流程
3. 创建配置了「抵押权人不签署」规则的订单，观察抵押权人签署流程","1. 抵押权人在车务通签署平台里签署（走法大大签署）
2. 代理人签署后，推送给抵押权人，抵押权人在自己的系统内签署，再推送中瑞签署后的文件
3. 合同签署流程中没有抵押权人签署的环节"
TC019,【抵押权人签署配置】配置按钮弹窗显示验证,P1,,"1. 点击[配置]按钮，观察弹窗显示
2. 查看弹窗标题和文字说明","1. 显示【配置抵押权人签署规则】弹窗
2. 弹窗文字说明显示「默认下单时，抵押权人是在车务通系统里签署!」"
TC020,【抵押权人签署配置】配置字段验证,P0,,"1. 在配置弹窗中不填写客户名称，其他正常填写，点击[确定]按钮，观察提示信息
2. 在配置弹窗中不填写抵押权人，其他正常填写，点击[确定]按钮，观察提示信息
3. 在配置弹窗中不填写签署方式，其他正常填写，点击[确定]按钮，观察提示信息
4. 填写客户名称、抵押权人、签署方式，点击[确定]按钮，观察配置结果","1. 给出客户名称必填的错误提示
2. 给出抵押权人必填的错误提示
3. 给出签署方式必填的错误提示
4. 成功添加1条抵押权人签署规则"
TC021,【抵押权人签署配置】抵押权人下拉选项验证,P1,,"1. 在配置弹窗中点击抵押权人下拉框，查看选项来源","1. 抵押权人下拉选项显示政策管家的抵押权人数据"
TC022,【抵押权人签署配置】签署方式选项验证,P1,,"1. 在配置弹窗中点击签署方式下拉框，查看可选项","1. 签署方式可选择「抵押权人自己用印」、「抵押权人不签署」两个选项"
TC023,【抵押权人签署配置】重复配置验证,P0,存在客户名称+抵押权人的签署规则,"1. 尝试新增已存在的客户+抵押权人的抵押权人签署规则，观察系统提示","1. 提示无法新增重复的客户+抵押权人签署规则"
TC024,【抵押权人签署配置】删除按钮功能验证,P1,存在抵押权人签署配置数据,"1. 点击任意一条记录的[删除]按钮，观察弹窗显示
2. 在删除确认弹窗中点击[取消]按钮，观察弹窗和数据变化
3. 再次点击[删除]按钮，在删除确认弹窗中点击[确定]按钮，观察弹窗和数据变化","1. 显示删除确认弹窗，提示文字为「确定要删除本条客户的抵押权人签署规则吗？」
2. 弹窗关闭，该条数据未被删除，仍显示在列表中
3. 弹窗关闭，该条数据被删除，不再显示在列表中"
TC025,【客户管理】现场照配置与抵押权人签署配置数据一致性验证,P2,"存在现场照配置数据;存在抵押权人签署配置数据","1. 在现场照配置页面查看客户数据
2. 在抵押权人签署配置页面查看相同客户数据
3. 验证两个页面客户信息的一致性","1. 现场照配置页面显示完整的客户配置信息
2. 抵押权人签署配置页面显示对应客户的签署配置信息
3. 两个页面的客户基础信息保持一致"
TC026,【客户管理】权限控制验证,P1,,"1. 使用有权限的用户访问现场照配置和抵押权人签署配置功能
2. 使用无权限的用户尝试访问这些功能","1. 有权限用户可以正常访问和操作所有功能
2. 无权限用户无法访问或显示权限不足提示"
TC027,【客户管理】配置规则生效时间验证,P2,,"1. 新增现场照配置规则后，立即创建符合条件的订单
2. 新增抵押权人签署配置规则后，立即创建符合条件的订单","1. 新增的现场照配置规则立即生效，订单应用新规则
2. 新增的抵押权人签署配置规则立即生效，订单应用新规则"
TC028,【客户管理】配置规则删除后影响验证,P2,存在已应用配置规则的订单,"1. 删除现场照配置规则后，查看已有订单的现场照设置
2. 删除抵押权人签署配置规则后，查看已有订单的签署流程","1. 删除配置规则后，已有订单的现场照设置不受影响
2. 删除配置规则后，已有订单的签署流程不受影响"
TC029,【客户管理】异常情况处理验证,P2,,"1. 在网络异常情况下尝试新增配置规则
2. 在网络异常情况下尝试删除配置规则
3. 验证异常情况的错误提示","1. 网络异常时新增配置显示适当的错误提示
2. 网络异常时删除配置显示适当的错误提示
3. 异常情况下有明确的用户提示信息"
TC030,【客户管理】大数据量性能验证,P2,存在大量客户配置数据,"1. 在大数据量情况下进行搜索操作
2. 在大数据量情况下进行配置新增操作
3. 验证系统响应性能","1. 搜索操作响应时间在合理范围内
2. 配置新增操作响应时间在合理范围内
3. 系统在大数据量下保持稳定运行"
