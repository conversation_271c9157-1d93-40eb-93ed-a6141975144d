用例编号,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,【查抵解押】页面标题显示验证,P1,,"1. 在微信端进入查询线上抵押订单功能页面，查看页面标题显示","1. 页面标题显示为「查询线上抵解押订单」"
TC002,【查抵解押】菜单名称显示验证,P1,,"1. 在微信端查看菜单列表，找到查询线上抵押订单功能的菜单项","1. 菜单名称显示为「查抵解押」"
TC003,【查抵解押】线上抵押订单查询验证,P0,存在线上抵押订单数据,"1. 在查询页面进行搜索，观察线上抵押订单的显示情况
2. 查看线上抵押订单的详细信息显示","1. 查询结果中正确显示线上抵押订单数据
2. 线上抵押订单信息完整准确显示"
TC004,【查抵解押】线上解押订单查询验证,P0,存在线上解押订单数据,"1. 在查询页面进行搜索，观察线上解押订单的显示情况
2. 查看线上解押订单的详细信息显示","1. 查询结果中正确显示线上解押订单数据
2. 线上解押订单信息完整准确显示"
TC005,【查抵解押】线上抵押签注订单查询验证,P0,存在线上抵押签注订单数据,"1. 在查询页面进行搜索，观察线上抵押签注订单的显示情况
2. 查看线上抵押签注订单的详细信息显示","1. 查询结果中正确显示线上抵押签注订单数据
2. 线上抵押签注订单信息完整准确显示"
TC006,【查抵解押】混合订单类型查询验证,P1,"存在线上抵押、线上解押、线上抵押签注订单数据","1. 在查询页面进行搜索，观察所有类型订单的混合显示情况
2. 验证不同类型订单的区分显示","1. 查询结果中同时显示线上抵押、线上解押、线上抵押签注三种类型的订单
2. 不同类型订单有明确的标识或分类显示"
TC007,【查抵解押】空数据查询验证,P2,不存在任何相关订单数据,"1. 在查询页面进行搜索，观察空数据时的显示情况","1. 页面显示无数据提示信息或空列表状态"
TC008,【查抵解押】查询功能响应性能验证,P2,存在大量订单数据,"1. 在查询页面进行搜索，观察查询响应时间
2. 测试查询结果的加载速度","1. 查询响应时间在合理范围内（通常3秒内）
2. 查询结果正常加载显示"
TC009,【查抵解押】订单数据完整性验证,P1,存在各类型订单数据,"1. 查询并查看订单列表中的字段显示
2. 进入订单详情查看完整信息","1. 订单列表显示必要的关键字段信息
2. 订单详情显示完整的订单信息"
TC010,【查抵解押】微信端适配性验证,P1,,"1. 在不同微信版本中访问查询功能
2. 在不同手机屏幕尺寸下查看页面显示
3. 测试页面的触摸操作响应","1. 功能在不同微信版本中正常运行
2. 页面在不同屏幕尺寸下正常显示和适配
3. 触摸操作响应正常"
TC011,【查抵解押】订单状态显示验证,P1,存在不同状态的订单数据,"1. 查询并查看不同状态订单的显示情况
2. 验证订单状态的准确性","1. 不同状态的订单正确显示对应的状态信息
2. 订单状态信息准确反映实际业务状态"
TC012,【查抵解押】查询权限验证,P0,,"1. 使用有权限的用户账号进行查询
2. 使用无权限的用户账号尝试访问功能","1. 有权限用户可以正常查询订单信息
2. 无权限用户无法访问或显示权限不足提示"
TC013,【查抵解押】数据安全性验证,P0,存在敏感订单数据,"1. 查询订单信息，检查敏感数据的显示处理
2. 验证用户只能查看自己权限范围内的订单","1. 敏感数据进行适当的脱敏或加密显示
2. 用户只能查看自己有权限的订单数据"
TC014,【查抵解押】网络异常处理验证,P2,,"1. 在网络不稳定环境下进行查询操作
2. 在网络中断后恢复时测试功能","1. 网络异常时显示适当的错误提示
2. 网络恢复后功能正常使用"
TC015,【查抵解押】功能入口访问验证,P1,,"1. 从微信端主菜单进入查抵解押功能
2. 验证功能入口的可访问性","1. 可以正常从主菜单进入查抵解押功能
2. 功能入口清晰易找，用户体验良好"
