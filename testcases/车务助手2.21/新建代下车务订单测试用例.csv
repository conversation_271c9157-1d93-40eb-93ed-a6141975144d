用例编号,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,【新建/代下车务订单】抵押合同生成方式验证,P0,业务类型=线上抵押或线上抵押数据推送,"1. 进入新建/代下车务订单页面，选择业务类型为线上抵押，查看抵押合同生成方式字段显示
2. 验证合同生成规则设置为上传已签署文件时，选项显示和用户修改权限
3. 验证合同生成规则设置为上传文件在线签署时，选项显示和用户修改权限及相关字段
4. 验证合同生成规则设置为模版合同在线签署时，选项显示和系统行为
5. 验证未设置合同生成规则时，点击提交按钮的系统响应","1. 业务类型为线上抵押时，抵押合同生成方式字段正常显示，必填且为单选
2. 规则设置为上传已签署文件时，选中该选项且用户不能修改，需要上传pdf文件，不参与电子合同在线签署流程
3. 规则设置为上传文件在线签署时，选中该选项且用户不能修改，需要上传pdf文件，显示「参与人、签章位置关键字」，走线上签署流程
4. 规则设置为模版合同在线签署时，选中该选项，系统自动灌数生成电子合同，走线上签署流程
5. 未设置合同生成规则时，提交时显示错误提示"
TC002,【新建/代下车务订单】机动车所有人委托书生成方式验证,P0,"业务类型=线上抵押、线上解押或线上抵押数据推送","1. 进入新建/代下车务订单页面，选择业务类型为线上抵押，查看机动车所有人委托书生成方式字段显示
2. 验证合同生成规则设置为上传已签署文件时的选项状态和功能
3. 验证合同生成规则设置为上传文件在线签署时的选项状态和功能
4. 验证合同生成规则设置为模版合同在线签署时的选项状态和功能
5. 验证未设置合同生成规则时的提交验证","1. 业务类型符合条件时，机动车所有人委托书生成方式字段正常显示，必填且为单选
2. 规则设置为上传已签署文件时，选中该选项且用户不能修改，需要上传pdf文件，不参与电子合同在线签署流程
3. 规则设置为上传文件在线签署时，选中该选项且用户不能修改，需要上传pdf文件，显示「参与人、签章位置关键字」，走线上签署流程
4. 规则设置为模版合同在线签署时，选中该选项，系统自动灌数生成电子合同，走线上签署流程
5. 未设置合同生成规则时，提交时显示错误提示"
TC003,【新建/代下车务订单】抵押权人委托书生成方式验证,P0,"业务类型=线上抵押、线上解押或线上抵押数据推送","1. 进入新建/代下车务订单页面，选择业务类型为线上解押，查看抵押权人委托书生成方式字段显示
2. 验证合同生成规则设置为上传已签署文件时的选项状态和功能
3. 验证合同生成规则设置为上传文件在线签署时的选项状态和功能
4. 验证合同生成规则设置为模版合同在线签署时的选项状态和功能
5. 验证未设置合同生成规则时的提交验证","1. 业务类型符合条件时，抵押权人委托书生成方式字段正常显示，必填且为单选
2. 规则设置为上传已签署文件时，选中该选项且用户不能修改，需要上传pdf文件，不参与电子合同在线签署流程
3. 规则设置为上传文件在线签署时，选中该选项且用户不能修改，需要上传pdf文件，显示「参与人、签章位置关键字」，走线上签署流程
4. 规则设置为模版合同在线签署时，选中该选项，系统自动灌数生成电子合同，走线上签署流程
5. 未设置合同生成规则时，提交时显示错误提示"
TC004,【新建/代下车务订单】申请表生成方式验证,P0,"业务类型=线上抵押、线上解押或线上抵押数据推送","1. 进入新建/代下车务订单页面，选择业务类型为线上抵押数据推送，查看申请表生成方式字段显示
2. 验证合同生成规则设置为上传已签署文件时的选项状态和功能
3. 验证合同生成规则设置为上传文件在线签署时的选项状态和功能
4. 验证合同生成规则设置为模版合同在线签署时的选项状态和功能
5. 验证未设置合同生成规则时的提交验证","1. 业务类型符合条件时，申请表生成方式字段正常显示，必填且为单选
2. 规则设置为上传已签署文件时，选中该选项且用户不能修改，需要上传pdf文件，不参与电子合同在线签署流程
3. 规则设置为上传文件在线签署时，选中该选项且用户不能修改，需要上传pdf文件，显示「参与人、签章位置关键字」，走线上签署流程
4. 规则设置为模版合同在线签署时，选中该选项，系统自动灌数生成电子合同，走线上签署流程
5. 未设置合同生成规则时，提交时显示错误提示"
TC005,【新建/代下车务订单】所有人声明书生成方式验证,P0,业务类型=线上抵押或线上抵押数据推送,"1. 进入新建/代下车务订单页面，选择业务类型为线上抵押，查看所有人声明书生成方式字段显示
2. 验证合同生成规则设置为上传已签署文件时的选项状态和功能
3. 验证合同生成规则设置为上传文件在线签署时的选项状态和功能
4. 验证合同生成规则设置为模版合同在线签署时的选项状态和功能
5. 验证未设置合同生成规则时的提交验证","1. 业务类型符合条件时，所有人声明书生成方式字段正常显示，必填且为单选
2. 规则设置为上传已签署文件时，选中该选项且用户不能修改，需要上传pdf文件，不参与电子合同在线签署流程
3. 规则设置为上传文件在线签署时，选中该选项且用户不能修改，需要上传pdf文件，显示「参与人、签章位置关键字」，走线上签署流程
4. 规则设置为模版合同在线签署时，选中该选项，系统自动灌数生成电子合同，走线上签署流程
5. 未设置合同生成规则时，提交时显示错误提示"
TC006,【新建/代下车务订单】抵押物清单生成方式验证,P0,业务类型=线上抵押或线上抵押数据推送,"1. 进入新建/代下车务订单页面，选择业务类型为线上抵押数据推送，查看抵押物清单生成方式字段显示
2. 验证合同生成规则设置为上传已签署文件时的选项状态和功能
3. 验证合同生成规则设置为上传文件在线签署时的选项状态和功能
4. 验证合同生成规则设置为模版合同在线签署时的选项状态和功能
5. 验证未设置合同生成规则时的提交验证","1. 业务类型符合条件时，抵押物清单生成方式字段正常显示，必填且为单选
2. 规则设置为上传已签署文件时，选中该选项且用户不能修改，需要上传pdf文件，不参与电子合同在线签署流程
3. 规则设置为上传文件在线签署时，选中该选项且用户不能修改，需要上传pdf文件，显示「参与人、签章位置关键字」，走线上签署流程
4. 规则设置为模版合同在线签署时，选中该选项，系统自动灌数生成电子合同，走线上签署流程
5. 未设置合同生成规则时，提交时显示错误提示"
TC007,【新建/代下车务订单】抵押合同文件大小验证,P1,选择上传抵押合同文件,"1. 在抵押合同上传区域选择一个大于700KB的PDF文件进行上传，观察系统提示
2. 在抵押合同上传区域选择一个等于700KB的PDF文件进行上传，观察上传结果
3. 在抵押合同上传区域选择一个小于700KB的PDF文件进行上传，观察上传结果","1. 上传大于700KB的文件时，系统提示「请上传700KB以下的文件」
2. 上传等于700KB的文件时，上传成功
3. 上传小于700KB的文件时，上传成功"
TC008,【新建/代下车务订单】申请表文件大小验证,P1,选择上传申请表文件,"1. 在申请表上传区域选择一个大于600KB的PDF文件进行上传，观察系统提示
2. 在申请表上传区域选择一个等于600KB的PDF文件进行上传，观察上传结果
3. 在申请表上传区域选择一个小于600KB的PDF文件进行上传，观察上传结果","1. 上传大于600KB的文件时，系统提示「请上传600KB以下的文件」
2. 上传等于600KB的文件时，上传成功
3. 上传小于600KB的文件时，上传成功"
TC009,【新建/代下车务订单】抵押权人授权委托书文件大小验证,P1,选择上传抵押权人授权委托书文件,"1. 在抵押权人授权委托书上传区域选择一个大于500KB的PDF文件进行上传，观察系统提示
2. 在抵押权人授权委托书上传区域选择一个等于500KB的PDF文件进行上传，观察上传结果
3. 在抵押权人授权委托书上传区域选择一个小于500KB的PDF文件进行上传，观察上传结果","1. 上传大于500KB的文件时，系统提示「请上传500KB以下的文件」
2. 上传等于500KB的文件时，上传成功
3. 上传小于500KB的文件时，上传成功"
TC010,【新建/代下车务订单】车主授权委托书文件大小验证,P1,选择上传车主授权委托书文件,"1. 在车主授权委托书上传区域选择一个大于500KB的PDF文件进行上传，观察系统提示
2. 在车主授权委托书上传区域选择一个等于500KB的PDF文件进行上传，观察上传结果
3. 在车主授权委托书上传区域选择一个小于500KB的PDF文件进行上传，观察上传结果","1. 上传大于500KB的文件时，系统提示「请上传500KB以下的文件」
2. 上传等于500KB的文件时，上传成功
3. 上传小于500KB的文件时，上传成功"
TC011,【新建/代下车务订单】所有人声明书文件大小验证,P1,选择上传所有人声明书文件,"1. 在所有人声明书上传区域选择一个大于500KB的PDF文件进行上传，观察系统提示
2. 在所有人声明书上传区域选择一个等于500KB的PDF文件进行上传，观察上传结果
3. 在所有人声明书上传区域选择一个小于500KB的PDF文件进行上传，观察上传结果","1. 上传大于500KB的文件时，系统提示「请上传500KB以下的文件」
2. 上传等于500KB的文件时，上传成功
3. 上传小于500KB的文件时，上传成功"
TC012,【新建/代下车务订单】抵押物清单文件大小验证,P1,选择上传抵押物清单文件,"1. 在抵押物清单上传区域选择一个大于500KB的PDF文件进行上传，观察系统提示
2. 在抵押物清单上传区域选择一个等于500KB的PDF文件进行上传，观察上传结果
3. 在抵押物清单上传区域选择一个小于500KB的PDF文件进行上传，观察上传结果","1. 上传大于500KB的文件时，系统提示「请上传500KB以下的文件」
2. 上传等于500KB的文件时，上传成功
3. 上传小于500KB的文件时，上传成功"
TC013,【新建/代下车务订单】提交按钮跳过签署环节验证,P0,"6个生成方式都选择上传已签署文件，且匹配到代理人","1. 在新建/代下车务订单页面，将抵押合同、机动车所有人委托书、抵押权人委托书、申请表、所有人声明书、抵押物清单的生成方式都选择为上传已签署文件
2. 确保系统匹配到代理人
3. 点击[提交]按钮，观察订单处理流程和状态变化","1. 所有6个生成方式都成功选择为上传已签署文件
2. 系统成功匹配到代理人
3. 点击提交后，订单跳过合同签署环节，直接推送交科所，订单状态=处理中，工单状态=预审中，合同状态=签署成功"
TC014,【新建/代下车务订单】提交按钮走签署环节验证,P0,存在非上传已签署文件的生成方式选择,"1. 在新建/代下车务订单页面，将部分生成方式选择为上传文件在线签署或模版合同在线签署
2. 完成其他必填信息填写
3. 点击[提交]按钮，观察订单处理流程","1. 部分生成方式选择为非上传已签署文件
2. 其他必填信息填写完整
3. 点击提交后，订单需要走合同签署环节"
TC015,【新建/代下车务订单】业务类型显示条件验证,P1,,"1. 进入新建/代下车务订单页面，选择业务类型为线上抵押，查看相关字段显示
2. 选择业务类型为线上解押，查看相关字段显示
3. 选择业务类型为线上抵押数据推送，查看相关字段显示
4. 选择其他业务类型，查看相关字段显示","1. 业务类型为线上抵押时，显示抵押合同、机动车所有人委托书、抵押权人委托书、申请表、所有人声明书、抵押物清单生成方式字段
2. 业务类型为线上解押时，显示机动车所有人委托书、抵押权人委托书、申请表生成方式字段
3. 业务类型为线上抵押数据推送时，显示抵押合同、机动车所有人委托书、抵押权人委托书、申请表、所有人声明书、抵押物清单生成方式字段
4. 其他业务类型时，不显示相关生成方式字段"
