用例编号,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,【我的签署订单】待签署和已签署数量统计验证,P0,,"1. 创建订单并匹配代理人，合同状态变为[待车主签署]，查看待签署数量变化
2. 完成签署操作，查看待签署和已签署数量变化
3. 修改代理人重新生成合同，查看待签署和已签署数量变化","1. 下单后匹配代理人，待签署数量+1
2. 签署完成后，待签署数量-1，已签署数量+1
3. 修改代理人重新生成合同后，已签署数量-1，待签署数量+1"
TC002,【我的签署订单】单份签署按钮显示验证,P0,"存在不同类型的待签署订单：安吉预备线上解押订单、车晓线上抵押数据推送订单、易鑫新安线上抵押订单、易鑫非新安线上抵押订单、金融机构订单","1. 进入我的签署订单页面，切换到[待签署]tab，查看页面显示
2. 查看安吉预备线上解押订单的[单份签署]按钮显示情况
3. 查看车晓线上抵押数据推送订单的[单份签署]按钮显示情况
4. 查看易鑫新安线上抵押订单的[单份签署]按钮显示情况
5. 查看易鑫非新安线上抵押订单的[单份签署]按钮显示情况
6. 使用金融机构账号登录，查看金融机构订单的[单份签署]按钮显示情况","1. 页面正确显示[待签署]tab内容
2. 安吉预备线上解押订单正确显示[单份签署]按钮
3. 车晓线上抵押数据推送订单正确显示[单份签署]按钮
4. 易鑫新安线上抵押订单正确显示[单份签署]按钮
5. 易鑫非新安线上抵押订单正确显示[单份签署]按钮
6. 金融机构订单正确显示[单份签署]按钮"
TC003,【我的签署订单】单份签署按钮跳转功能验证,P0,存在待签署订单,"1. 在[待签署]tab中点击任意订单的[单份签署]按钮，观察页面跳转情况","1. 点击[单份签署]按钮后跳转到【签署任务详情】页面"
TC004,【我的签署订单】签署流程验证,P0,存在待签署订单,"1. 在现场照为空的订单签署任务详情页面点击[签署]按钮，观察页面跳转
2. 在补充身份信息页面补充信息后，观察页面跳转
3. 在现场照不为空的订单签署任务详情页面点击[签署]按钮，观察页面跳转","1. 现场照为空时，点击[签署]按钮跳转到【补充身份信息】页面
2. 补充信息后，跳转到【文档签署】页面（法大大的）
3. 现场照不为空时，点击[签署]按钮直接跳转到【文档签署】页面（法大大的）"
TC005,【我的签署订单】已签署tab显示验证,P1,存在已签署的订单,"1. 进入我的签署订单页面，切换到[已签署]tab
2. 查看已签署订单的显示情况","1. 页面显示[已签署]tab内容
2. 正确显示所有已签署的订单信息"
TC006,【我的签署订单】待签署tab和已签署tab切换验证,P1,,"1. 进入我的签署订单页面，默认查看当前tab
2. 点击[待签署]tab，观察页面内容变化
3. 点击[已签署]tab，观察页面内容变化","1. 页面默认显示其中一个tab的内容
2. 点击[待签署]tab后，显示待签署订单列表
3. 点击[已签署]tab后，显示已签署订单列表"
TC007,【我的签署订单】订单状态实时更新验证,P1,存在正在处理的订单,"1. 在待签署订单列表中查看订单状态
2. 完成签署操作后，刷新页面查看订单状态变化
3. 查看订单是否从待签署移动到已签署","1. 待签署列表显示正确的订单状态
2. 签署完成后，订单状态实时更新
3. 订单正确从待签署列表移动到已签署列表"
TC008,【我的签署订单】数据刷新和同步验证,P2,存在多个待签署订单,"1. 在我的签署订单页面停留一段时间，观察数据是否自动刷新
2. 在其他页面完成签署操作后，返回我的签署订单页面查看数据同步情况","1. 页面数据能够及时刷新更新
2. 其他页面的操作能够同步反映到我的签署订单页面"
TC009,【我的签署订单】异常情况处理验证,P2,,"1. 在网络异常情况下访问我的签署订单页面
2. 在签署过程中出现网络中断，观察系统处理
3. 验证异常情况的错误提示","1. 网络异常时显示适当的错误提示
2. 签署过程中网络中断时有相应的处理机制
3. 异常情况下有明确的用户提示信息"
TC010,【我的签署订单】性能和响应验证,P2,存在大量签署订单数据,"1. 在大量数据情况下访问我的签署订单页面，观察加载速度
2. 测试tab切换的响应速度
3. 测试签署操作的响应时间","1. 大量数据下页面加载速度在合理范围内
2. tab切换响应迅速
3. 签署操作响应时间在合理范围内"
