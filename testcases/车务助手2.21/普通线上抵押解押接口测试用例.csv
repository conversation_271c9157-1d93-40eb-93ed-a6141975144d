用例编号,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,【普通线上抵押接口】未设置现场照配置时现场照验证,P0,"业务类型为线上抵押;未设置现场照配置","1. 通过接口创建线上抵押订单，不上传现场照，尝试下单
2. 通过接口创建线上抵押订单，上传现场照，尝试下单","1. 没上传现场照时，不能下单成功，返回相应错误信息
2. 上传现场照后，可以下单成功"
TC002,【普通线上解押接口】未设置现场照配置时现场照验证,P0,"业务类型为线上解押;未设置现场照配置","1. 通过接口创建线上解押订单，不上传现场照，尝试下单
2. 通过接口创建线上解押订单，上传现场照，尝试下单","1. 没上传现场照时，不能下单成功，返回相应错误信息
2. 上传现场照后，可以下单成功"
TC003,【普通线上抵押接口】现场照配置为选填时客户不传现场照验证,P1,"业务类型为线上抵押;现场照配置为选填","1. 通过接口创建线上抵押订单，客户不传现场照，尝试下单
2. 查看订单签署流程中现场照上传环节","1. 客户不传现场照时，可以下单成功
2. 车主签署合同时需要上传现场照"
TC004,【普通线上解押接口】现场照配置为选填时客户不传现场照验证,P1,"业务类型为线上解押;现场照配置为选填","1. 通过接口创建线上解押订单，客户不传现场照，尝试下单
2. 查看订单签署流程中现场照上传环节","1. 客户不传现场照时，可以下单成功
2. 车主签署合同时需要上传现场照"
TC005,【普通线上抵押接口】现场照配置为选填时客户已上传现场照验证,P1,"业务类型为线上抵押;现场照配置为选填","1. 通过接口创建线上抵押订单，客户已上传现场照，尝试下单
2. 查看订单签署流程","1. 客户已上传现场照时，可以下单成功
2. 直接进入签署合同流程，无需再次上传现场照"
TC006,【普通线上解押接口】现场照配置为选填时客户已上传现场照验证,P1,"业务类型为线上解押;现场照配置为选填","1. 通过接口创建线上解押订单，客户已上传现场照，尝试下单
2. 查看订单签署流程","1. 客户已上传现场照时，可以下单成功
2. 直接进入签署合同流程，无需再次上传现场照"
TC007,【普通线上抵押接口】现场照格式和大小验证,P0,业务类型为线上抵押,"1. 通过接口上传500kb以下的jpg格式现场照，尝试下单
2. 通过接口上传500kb以下的png格式现场照，尝试下单
3. 通过接口上传500kb以上的jpg格式现场照，尝试下单","1. 上传500kb以下的jpg文件时，可以下单成功
2. 上传500kb以下的其他格式文件时，不能下单成功，返回格式错误信息
3. 上传500kb以上的jpg文件时，不能下单成功，返回文件过大错误信息"
TC008,【普通线上解押接口】现场照格式和大小验证,P0,业务类型为线上解押,"1. 通过接口上传500kb以下的jpg格式现场照，尝试下单
2. 通过接口上传500kb以下的png格式现场照，尝试下单
3. 通过接口上传500kb以上的jpg格式现场照，尝试下单","1. 上传500kb以下的jpg文件时，可以下单成功
2. 上传500kb以下的其他格式文件时，不能下单成功，返回格式错误信息
3. 上传500kb以上的jpg文件时，不能下单成功，返回文件过大错误信息"
TC009,【普通线上抵押接口】抵押合同文件上传验证,P0,"业务类型为线上抵押;合同生成规则设置为上传已签署文件或上传文件在线签署","1. 通过接口创建线上抵押订单，不上传抵押合同文件，尝试下单
2. 通过接口上传700kb以下的pdf格式抵押合同，尝试下单
3. 通过接口上传700kb以下的doc格式抵押合同，尝试下单
4. 通过接口上传700kb以上的pdf格式抵押合同，尝试下单","1. 不上传抵押合同文件时，不能下单成功，返回相应错误信息
2. 上传700kb以下的pdf文件时，可以下单成功
3. 上传700kb以下的其他格式文件时，不能下单成功，返回格式错误信息
4. 上传700kb以上的pdf文件时，不能下单成功，返回文件过大错误信息"
TC010,【普通线上解押接口】抵押合同特殊处理验证,P1,业务类型为线上解押,"1. 通过接口创建线上解押订单，填写抵押合同信息，尝试下单","1. 线上解押订单中填写的抵押合同信息被直接忽略，不影响下单"
TC011,【普通线上抵押接口】申请表文件上传验证,P0,"业务类型为线上抵押;合同生成规则设置为上传已签署文件或上传文件在线签署","1. 通过接口创建线上抵押订单，不上传申请表文件，尝试下单
2. 通过接口上传600kb以下的pdf格式申请表，尝试下单
3. 通过接口上传600kb以下的doc格式申请表，尝试下单
4. 通过接口上传600kb以上的pdf格式申请表，尝试下单","1. 不上传申请表文件时，不能下单成功，返回相应错误信息
2. 上传600kb以下的pdf文件时，可以下单成功
3. 上传600kb以下的其他格式文件时，不能下单成功，返回格式错误信息
4. 上传600kb以上的pdf文件时，不能下单成功，返回文件过大错误信息"
TC012,【普通线上解押接口】申请表文件上传验证,P0,"业务类型为线上解押;合同生成规则设置为上传已签署文件或上传文件在线签署","1. 通过接口创建线上解押订单，不上传申请表文件，尝试下单
2. 通过接口上传600kb以下的pdf格式申请表，尝试下单
3. 通过接口上传600kb以下的doc格式申请表，尝试下单
4. 通过接口上传600kb以上的pdf格式申请表，尝试下单","1. 不上传申请表文件时，不能下单成功，返回相应错误信息
2. 上传600kb以下的pdf文件时，可以下单成功
3. 上传600kb以下的其他格式文件时，不能下单成功，返回格式错误信息
4. 上传600kb以上的pdf文件时，不能下单成功，返回文件过大错误信息"
TC013,【普通线上抵押接口】车主委托书文件上传验证,P0,"业务类型为线上抵押;合同生成规则设置为上传已签署文件或上传文件在线签署","1. 通过接口创建线上抵押订单，不上传车主委托书文件，尝试下单
2. 通过接口上传500kb以下的pdf格式车主委托书，尝试下单
3. 通过接口上传500kb以下的doc格式车主委托书，尝试下单
4. 通过接口上传500kb以上的pdf格式车主委托书，尝试下单","1. 不上传车主委托书文件时，不能下单成功，返回相应错误信息
2. 上传500kb以下的pdf文件时，可以下单成功
3. 上传500kb以下的其他格式文件时，不能下单成功，返回格式错误信息
4. 上传500kb以上的pdf文件时，不能下单成功，返回文件过大错误信息"
TC014,【普通线上解押接口】车主委托书文件上传验证,P0,"业务类型为线上解押;合同生成规则设置为上传已签署文件或上传文件在线签署","1. 通过接口创建线上解押订单，不上传车主委托书文件，尝试下单
2. 通过接口上传500kb以下的pdf格式车主委托书，尝试下单
3. 通过接口上传500kb以下的doc格式车主委托书，尝试下单
4. 通过接口上传500kb以上的pdf格式车主委托书，尝试下单","1. 不上传车主委托书文件时，不能下单成功，返回相应错误信息
2. 上传500kb以下的pdf文件时，可以下单成功
3. 上传500kb以下的其他格式文件时，不能下单成功，返回格式错误信息
4. 上传500kb以上的pdf文件时，不能下单成功，返回文件过大错误信息"
TC015,【普通线上抵押接口】抵押权人委托书文件上传验证,P0,"业务类型为线上抵押;合同生成规则设置为上传已签署文件或上传文件在线签署","1. 通过接口创建线上抵押订单，不上传抵押权人委托书文件，尝试下单
2. 通过接口上传500kb以下的pdf格式抵押权人委托书，尝试下单
3. 通过接口上传500kb以下的doc格式抵押权人委托书，尝试下单
4. 通过接口上传500kb以上的pdf格式抵押权人委托书，尝试下单","1. 不上传抵押权人委托书文件时，不能下单成功，返回相应错误信息
2. 上传500kb以下的pdf文件时，可以下单成功
3. 上传500kb以下的其他格式文件时，不能下单成功，返回格式错误信息
4. 上传500kb以上的pdf文件时，不能下单成功，返回文件过大错误信息"
TC016,【普通线上解押接口】抵押权人委托书文件上传验证,P0,"业务类型为线上解押;合同生成规则设置为上传已签署文件或上传文件在线签署","1. 通过接口创建线上解押订单，不上传抵押权人委托书文件，尝试下单
2. 通过接口上传500kb以下的pdf格式抵押权人委托书，尝试下单
3. 通过接口上传500kb以下的doc格式抵押权人委托书，尝试下单
4. 通过接口上传500kb以上的pdf格式抵押权人委托书，尝试下单","1. 不上传抵押权人委托书文件时，不能下单成功，返回相应错误信息
2. 上传500kb以下的pdf文件时，可以下单成功
3. 上传500kb以下的其他格式文件时，不能下单成功，返回格式错误信息
4. 上传500kb以上的pdf文件时，不能下单成功，返回文件过大错误信息"
TC017,【普通线上抵押接口】所有人声明书文件上传验证,P0,"业务类型为线上抵押;合同生成规则设置为上传已签署文件或上传文件在线签署","1. 通过接口创建线上抵押订单，不上传所有人声明书文件，尝试下单
2. 通过接口上传500kb以下的pdf格式所有人声明书，尝试下单
3. 通过接口上传500kb以下的doc格式所有人声明书，尝试下单
4. 通过接口上传500kb以上的pdf格式所有人声明书，尝试下单","1. 不上传所有人声明书文件时，不能下单成功，返回相应错误信息
2. 上传500kb以下的pdf文件时，可以下单成功
3. 上传500kb以下的其他格式文件时，不能下单成功，返回格式错误信息
4. 上传500kb以上的pdf文件时，不能下单成功，返回文件过大错误信息"
TC018,【普通线上抵押接口】抵押物清单文件上传验证,P0,"业务类型为线上抵押;合同生成规则设置为上传已签署文件或上传文件在线签署","1. 通过接口创建线上抵押订单，不上传抵押物清单文件，尝试下单
2. 通过接口上传500kb以下的pdf格式抵押物清单，尝试下单
3. 通过接口上传500kb以下的doc格式抵押物清单，尝试下单
4. 通过接口上传500kb以上的pdf格式抵押物清单，尝试下单","1. 不上传抵押物清单文件时，不能下单成功，返回相应错误信息
2. 上传500kb以下的pdf文件时，可以下单成功
3. 上传500kb以下的其他格式文件时，不能下单成功，返回格式错误信息
4. 上传500kb以上的pdf文件时，不能下单成功，返回文件过大错误信息"
TC019,【普通线上解押接口】所有人声明书和抵押物清单特殊处理验证,P1,业务类型为线上解押,"1. 通过接口创建线上解押订单，填写所有人声明书信息，尝试下单
2. 通过接口创建线上解押订单，填写抵押物清单信息，尝试下单","1. 线上解押订单中填写的所有人声明书信息被直接忽略，不影响下单
2. 线上解押订单中填写的抵押物清单信息被直接忽略，不影响下单"
TC020,【普通线上抵押接口】完整订单创建验证,P0,"业务类型为线上抵押;所有合同生成规则正确配置","1. 通过接口创建线上抵押订单，上传所有必需的文件（现场照、抵押合同、申请表、车主委托书、抵押权人委托书、所有人声明书、抵押物清单），尝试下单","1. 所有文件格式和大小符合要求时，订单创建成功"
TC021,【普通线上解押接口】完整订单创建验证,P0,"业务类型为线上解押;所有合同生成规则正确配置","1. 通过接口创建线上解押订单，上传所有必需的文件（现场照、申请表、车主委托书、抵押权人委托书），尝试下单","1. 所有文件格式和大小符合要求时，订单创建成功"
TC022,【普通线上抵押接口】文件大小边界值验证,P2,业务类型为线上抵押,"1. 通过接口上传恰好500kb的jpg格式现场照，尝试下单
2. 通过接口上传恰好700kb的pdf格式抵押合同，尝试下单
3. 通过接口上传恰好600kb的pdf格式申请表，尝试下单
4. 通过接口上传恰好500kb的pdf格式委托书，尝试下单","1. 恰好500kb的jpg现场照可以上传成功
2. 恰好700kb的pdf抵押合同可以上传成功
3. 恰好600kb的pdf申请表可以上传成功
4. 恰好500kb的pdf委托书可以上传成功"
TC023,【普通线上解押接口】文件大小边界值验证,P2,业务类型为线上解押,"1. 通过接口上传恰好500kb的jpg格式现场照，尝试下单
2. 通过接口上传恰好600kb的pdf格式申请表，尝试下单
3. 通过接口上传恰好500kb的pdf格式委托书，尝试下单","1. 恰好500kb的jpg现场照可以上传成功
2. 恰好600kb的pdf申请表可以上传成功
3. 恰好500kb的pdf委托书可以上传成功"
TC024,【普通线上抵押接口】接口参数验证,P1,,"1. 通过接口发送缺少必填参数的请求
2. 通过接口发送参数格式错误的请求
3. 通过接口发送参数值超出范围的请求","1. 缺少必填参数时，返回参数缺失错误信息
2. 参数格式错误时，返回格式错误信息
3. 参数值超出范围时，返回参数值错误信息"
TC025,【普通线上解押接口】接口参数验证,P1,,"1. 通过接口发送缺少必填参数的请求
2. 通过接口发送参数格式错误的请求
3. 通过接口发送参数值超出范围的请求","1. 缺少必填参数时，返回参数缺失错误信息
2. 参数格式错误时，返回格式错误信息
3. 参数值超出范围时，返回参数值错误信息"
TC026,【普通线上抵押接口】并发请求验证,P2,,"1. 同时发送多个线上抵押订单创建请求
2. 验证系统并发处理能力","1. 系统能够正确处理并发请求，每个请求都得到正确响应"
TC027,【普通线上解押接口】并发请求验证,P2,,"1. 同时发送多个线上解押订单创建请求
2. 验证系统并发处理能力","1. 系统能够正确处理并发请求，每个请求都得到正确响应"
TC028,【普通线上抵押接口】接口响应时间验证,P2,,"1. 发送标准的线上抵押订单创建请求，记录响应时间
2. 验证接口性能指标","1. 接口响应时间在合理范围内（通常3秒内）"
TC029,【普通线上解押接口】接口响应时间验证,P2,,"1. 发送标准的线上解押订单创建请求，记录响应时间
2. 验证接口性能指标","1. 接口响应时间在合理范围内（通常3秒内）"
TC030,【普通线上抵押解押接口】错误处理和异常场景验证,P2,,"1. 发送格式错误的文件上传请求
2. 发送超大文件上传请求
3. 在网络异常情况下发送请求
4. 验证错误信息的准确性和友好性","1. 格式错误时返回明确的错误提示
2. 超大文件时返回文件大小限制提示
3. 网络异常时返回适当的错误信息
4. 所有错误信息准确且用户友好"
