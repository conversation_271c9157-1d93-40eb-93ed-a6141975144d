用例编号,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,【订单详情tab】页面显示内容验证,P1,存在订单数据,"1. 进入订单详情页面，点击订单详情tab
2. 查看页面显示的所有字段信息","1. 页面显示抵押合同生成方式、申请表生成方式、抵押权人委托书生成方式、车主委托书生成方式、所有人声明书生成方式、抵押清单生成方式、参与人、签章位置关键字等信息"
TC002,【订单详情tab】上传已签署文件展示验证,P1,存在生成方式为「上传已签署文件」的合同,"1. 在订单详情tab中查看生成方式为「上传已签署文件」的合同显示
2. 点击文件链接，观察文件查看功能","1. 合同显示为文件形式，可以点击查看
2. 点击后可以正常查看pdf文件内容"
TC003,【订单详情tab】上传文件在线签署展示验证,P1,存在生成方式为「上传文件在线签署」的合同,"1. 在订单详情tab中查看生成方式为「上传文件在线签署」的合同显示
2. 点击查看文件按钮，观察页面跳转","1. 合同显示查看文件按钮
2. 点击按钮跳转到【签署任务】页面，由法大大提供"
TC004,【订单详情tab】模版合同在线签署展示验证,P1,存在生成方式为「模版合同在线签署」的合同,"1. 在订单详情tab中查看生成方式为「模版合同在线签署」的合同显示
2. 点击查看文件按钮，观察页面跳转","1. 合同显示查看文件按钮
2. 点击按钮跳转到【签署任务】页面，由法大大提供"
TC005,【订单详情tab】未签署完成合同查看验证,P1,存在未签署完成的合同,"1. 在订单详情tab中查看未签署完成的合同显示
2. 点击查看文件按钮，观察页面跳转","1. 未签署完成的合同仍显示查看文件按钮
2. 点击按钮可以正常跳转到【签署任务】页面，由法大大提供"
TC006,【订单详情tab】线上抵押业务重签按钮显示条件验证,P0,"业务类型为线上抵押;订单状态为处理中;交科所受理状态不为受理成功/已归档;合同生成方式为上传文件在线签署或模版合同在线签署","1. 查看订单详情tab中的重签按钮显示情况
2. 验证重签按钮的显示条件","1. 满足条件的合同文件显示[重签]按钮
2. 不满足条件的合同文件不显示[重签]按钮"
TC007,【订单详情tab】线上解押业务重签按钮显示条件验证,P0,"业务类型为线上解押;订单状态为处理中;交科所受理状态不为受理成功/已归档;合同生成方式为上传文件在线签署或模版合同在线签署","1. 查看订单详情tab中的重签按钮显示情况
2. 验证重签按钮的显示条件","1. 满足条件的合同文件显示[重签]按钮
2. 不满足条件的合同文件不显示[重签]按钮"
TC008,【订单详情tab】线上抵押数据推送业务重签按钮显示条件验证,P0,"业务类型为线上抵押数据推送;订单状态为处理中;交科所受理状态不为受理成功/已归档;合同生成方式为上传文件在线签署或模版合同在线签署","1. 查看订单详情tab中的重签按钮显示情况
2. 验证重签按钮的显示条件","1. 满足条件的合同文件显示[重签]按钮
2. 不满足条件的合同文件不显示[重签]按钮"
TC009,【订单详情tab】重签按钮显示条件边界验证,P2,"存在不同状态和生成方式的订单数据","1. 验证订单状态不为处理中时重签按钮显示情况
2. 验证交科所受理状态为受理成功/已归档时重签按钮显示情况
3. 验证合同生成方式为上传已签署文件时重签按钮显示情况","1. 订单状态不为处理中时，不显示[重签]按钮
2. 交科所受理状态为受理成功/已归档时，不显示[重签]按钮
3. 合同生成方式为上传已签署文件时，不显示[重签]按钮"
TC010,【订单详情tab】合同均未签署时重签功能验证,P0,存在未签署的抵押合同和车主委托书,"1. 点击抵押合同的[重签]按钮，观察重签流程
2. 点击车主委托书的[重签]按钮，观察重签流程","1. 点击抵押合同[重签]按钮后，先撤销合同再重新签署合同
2. 点击车主委托书[重签]按钮后，先撤销合同再重新签署合同"
TC011,【订单详情tab】合同均已签署且订单未推送时重签功能验证,P0,"合同均已签署;订单未推送给壹好车服","1. 点击抵押权人委托书的[重签]按钮，观察重签流程和状态变化","1. 重新签署该合同，订单状态变为处理中，工单状态变为待签署"
TC012,【订单详情tab】合同均已签署且订单已推送时重签功能验证,P0,"合同均已签署;订单已推送给壹好车服且已收件、提交结果、归还","1. 点击抵押权人委托书的[重签]按钮，观察重签流程和状态变化","1. 
1、重新签署该合同
2、壹好车服工单暂停（冻结）
3、订单状态变为处理中
4、工单状态变为待签署
5、交科所受理状态变成验证成功
6、车务通工单状态变成待处理
7、壹好车服恢复"
TC013,【订单详情tab】参与人信息显示验证,P1,存在包含参与人信息的订单,"1. 在订单详情tab中查看参与人信息显示
2. 验证参与人信息的完整性和准确性","1. 参与人信息正确显示，包含所有相关的参与人数据
2. 参与人信息与订单实际配置一致"
TC014,【订单详情tab】签章位置关键字显示验证,P1,存在包含签章位置关键字的订单,"1. 在订单详情tab中查看签章位置关键字显示
2. 验证签章位置关键字的完整性和准确性","1. 签章位置关键字正确显示，包含所有配置的关键字
2. 签章位置关键字与订单实际配置一致"
TC015,【订单详情tab】不同合同类型混合显示验证,P1,存在包含多种合同类型的订单,"1. 在订单详情tab中查看包含多种合同类型的订单显示
2. 验证不同合同类型的区分显示","1. 不同合同类型（抵押合同、申请表、委托书等）分别显示对应的生成方式
2. 各合同类型的显示效果符合对应的展示规则"
TC016,【订单详情tab】页面刷新数据一致性验证,P2,存在订单数据,"1. 在订单详情tab页面进行刷新操作
2. 验证刷新后数据的一致性","1. 页面刷新后，所有显示信息保持一致
2. 重签按钮的显示状态保持正确"
TC017,【订单详情tab】权限控制验证,P1,,"1. 使用有权限的用户查看订单详情tab
2. 使用无权限的用户尝试查看订单详情tab","1. 有权限用户可以正常查看所有信息和操作按钮
2. 无权限用户无法查看或显示权限不足提示"
TC018,【订单详情tab】重签操作权限验证,P0,存在可重签的合同,"1. 使用有重签权限的用户点击[重签]按钮
2. 使用无重签权限的用户尝试点击[重签]按钮","1. 有权限用户可以正常执行重签操作
2. 无权限用户无法执行重签或显示权限不足提示"
TC019,【订单详情tab】重签操作确认机制验证,P1,存在可重签的合同,"1. 点击[重签]按钮，观察是否有确认提示
2. 在确认提示中选择取消，观察操作结果
3. 在确认提示中选择确定，观察操作结果","1. 点击[重签]按钮后显示确认提示信息
2. 选择取消后，重签操作不执行，页面状态不变
3. 选择确定后，执行重签操作，状态按预期变化"
TC020,【订单详情tab】异常情况处理验证,P2,,"1. 在网络异常情况下尝试查看文件
2. 在网络异常情况下尝试执行重签操作
3. 验证异常情况的错误提示","1. 网络异常时查看文件显示适当的错误提示
2. 网络异常时重签操作显示适当的错误提示
3. 异常情况下有明确的用户提示信息"
